-- 创建数据库和用户权限
USE collectloop_db;

-- 创建患者信息表
CREATE TABLE IF NOT EXISTS spt_patient (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '患者姓名',
    id_card VARCHAR(18) DEFAULT '' COMMENT '身份证号',
    mobile VARCHAR(20) DEFAULT '' COMMENT '手机号',
    sex VARCHAR(10) DEFAULT '' COMMENT '性别',
    age INT DEFAULT 0 COMMENT '年龄',
    hospitalization_no VARCHAR(50) NOT NULL COMMENT '住院号',
    sickbed_no VARCHAR(20) DEFAULT '' COMMENT '床位号',
    inhospital_time DATETIME NOT NULL COMMENT '入院时间',
    outhospital_time DATETIME NULL COMMENT '出院时间',
    status INT DEFAULT 1 COMMENT '状态：1-在院，2-出院',
    catetegory VARCHAR(50) DEFAULT '' COMMENT '类别',
    inpatient_ward VARCHAR(100) DEFAULT '' COMMENT '病区',
    doctor_id VARCHAR(50) DEFAULT '' COMMENT '医生ID',
    inpatient_info_id VARCHAR(100) DEFAULT '' COMMENT '住院信息ID',
    nurse_id VARCHAR(50) DEFAULT '' COMMENT '护士ID',
    birthday DATE NULL COMMENT '生日',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_hospitalization_no (hospitalization_no),
    INDEX idx_doctor_id (doctor_id),
    INDEX idx_status (status),
    INDEX idx_inhospital_time (inhospital_time),
    INDEX idx_outhospital_time (outhospital_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者信息表';

-- 创建用户信息表
CREATE TABLE IF NOT EXISTS spt_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(50) NOT NULL COMMENT '用户名/医生编号',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    role_id BIGINT DEFAULT 0 COMMENT '角色ID',
    dept_id BIGINT DEFAULT 0 COMMENT '科室ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_name (user_name),
    INDEX idx_role_id (role_id),
    INDEX idx_dept_id (dept_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建患者视图
CREATE OR REPLACE VIEW view_spt_patient AS
SELECT 
    id,
    name,
    id_card,
    mobile,
    sex,
    age,
    hospitalization_no,
    sickbed_no,
    inhospital_time,
    outhospital_time,
    status,
    catetegory,
    inpatient_ward,
    doctor_id,
    inpatient_info_id,
    nurse_id,
    birthday
FROM spt_patient;

-- 创建用户视图
CREATE OR REPLACE VIEW view_spt_user AS
SELECT 
    id,
    user_name,
    name,
    role_id,
    dept_id
FROM spt_user;
