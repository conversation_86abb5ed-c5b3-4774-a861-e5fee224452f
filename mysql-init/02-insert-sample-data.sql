-- 插入示例数据
USE collectloop_db;

-- 插入医生用户数据
INSERT INTO spt_user (user_name, name, role_id, dept_id) VALUES
('DOC001', '张医生', 1, 101),
('DOC002', '李医生', 1, 102),
('DOC003', '王医生', 1, 103),
('DOC004', '赵医生', 1, 101),
('DOC005', '陈医生', 1, 102),
('NURSE001', '护士小王', 2, 201),
('NURSE002', '护士小李', 2, 202);

-- 插入患者数据
INSERT INTO spt_patient (
    name, id_card, mobile, sex, age, hospitalization_no, sickbed_no,
    inhospital_time, outhospital_time, status, catetegory, inpatient_ward,
    doctor_id, inpatient_info_id, nurse_id, birthday
) VALUES
-- 在院患者
('张三', '110101199001011234', '13800138001', '男', 35, 'ZY202501001', 'A101', 
 '2025-01-01 10:30:00', NULL, 1, '内科', '内科一病区', 'DOC001', 'ZY2025010110:30:00', 'NURSE001', '1990-01-01'),

('李四', '110101199002022345', '13800138002', '女', 28, 'ZY202501002', 'A102',
 '2025-01-02 14:20:00', NULL, 1, '外科', '外科一病区', 'DOC002', 'ZY2025010214:20:00', 'NURSE002', '1990-02-02'),

('王五', '110101199003033456', '13800138003', '男', 42, 'ZY202501003', 'B201',
 '2025-01-03 09:15:00', NULL, 1, '骨科', '骨科病区', 'DOC003', 'ZY2025010309:15:00', 'NURSE001', '1990-03-03'),

('赵六', '110101199004044567', '13800138004', '女', 55, 'ZY202501004', 'C301',
 '2025-01-04 16:45:00', NULL, 1, '妇科', '妇科病区', 'DOC004', 'ZY2025010416:45:00', 'NURSE002', '1990-04-04'),

-- 已出院患者
('孙七', '110101199005055678', '13800138005', '男', 38, 'ZY202412001', 'A103',
 '2024-12-01 08:30:00', '2024-12-15 10:00:00', 2, '内科', '内科一病区', 'DOC001', 'ZY2024120108:30:00', 'NURSE001', '1990-05-05'),

('周八', '110101199006066789', '女', '13800138006', '女', 45, 'ZY202412002', 'B202',
 '2024-12-02 11:20:00', '2024-12-20 14:30:00', 2, '外科', '外科一病区', 'DOC002', 'ZY2024120211:20:00', 'NURSE002', '1990-06-06'),

('吴九', '110101199007077890', '13800138007', '男', 32, 'ZY202412003', 'C302',
 '2024-12-03 15:10:00', '2024-12-25 09:45:00', 2, '骨科', '骨科病区', 'DOC003', 'ZY2024120315:10:00', 'NURSE001', '1990-07-07'),

-- 最近入院的患者（用于测试时间戳功能）
('郑十', '110101199008088901', '13800138008', '女', 29, 'ZY202501005', 'A104',
 NOW() - INTERVAL 2 HOUR, NULL, 1, '内科', '内科一病区', 'DOC005', CONCAT('ZY', DATE_FORMAT(NOW(), '%Y%m%d%H:%i:%s')), 'NURSE001', '1990-08-08'),

('钱十一', '110101199009099012', '13800138009', '男', 51, 'ZY202501006', 'B203',
 NOW() - INTERVAL 1 HOUR, NULL, 1, '外科', '外科一病区', 'DOC001', CONCAT('ZY', DATE_FORMAT(NOW(), '%Y%m%d%H:%i:%s')), 'NURSE002', '1990-09-09');

-- 验证数据插入
SELECT '患者数据统计:' as info;
SELECT 
    status,
    CASE status 
        WHEN 1 THEN '在院' 
        WHEN 2 THEN '出院' 
        ELSE '未知' 
    END as status_name,
    COUNT(*) as count 
FROM spt_patient 
GROUP BY status;

SELECT '医生数据统计:' as info;
SELECT COUNT(*) as doctor_count FROM spt_user WHERE role_id = 1;

SELECT '护士数据统计:' as info;
SELECT COUNT(*) as nurse_count FROM spt_user WHERE role_id = 2;
