# MySQL 配置说明

## 问题解决

之前的错误是因为程序配置为使用Oracle数据库，但系统中没有安装Oracle客户端库。现在已经修改为支持MySQL数据库。

## 修改内容

1. **添加了MySQL驱动支持**
   - 在 `models/sybase.go` 中添加了 `github.com/go-sql-driver/mysql` 驱动
   - 添加了 `CreateMysqlDB` 函数来创建MySQL连接

2. **更新了模型层**
   - 在 `models/define.go` 中添加了 `mysqlDb` 变量
   - 添加了 `InitMysqlModel`、`MysqlDB`、`CloseMysqlDB` 函数

3. **更新了主程序**
   - 在 `main.go` 的switch语句中添加了 "mysql" 分支

4. **更新了数据访问层**
   - 在 `dao/his.go` 中添加了MySQL特定的SQL查询语句
   - 在 `dao/basepush.go` 中添加了MySQL的医生信息查询支持
   - 适配了MySQL的时间戳转换函数

5. **更新了配置文件**
   - 将 `DB_NAME` 从 "oracle" 改为 "mysql"
   - 更新了数据库连接参数为MySQL格式

## 配置MySQL

请根据你的实际MySQL环境修改 `conf/config.json` 中的以下配置：

```json
{
  "hospitalinfo": {
    "MSSQL_USERNAME": "你的MySQL用户名",
    "MSSQL_PASSWORD": "你的MySQL密码", 
    "MSSQL_HOST": "你的MySQL主机地址",
    "MSSQL_PORT": "3306",
    "MSSQL_DB_NAME": "你的数据库名",
    "DB_NAME": "mysql"
  }
}
```

## 示例配置

```json
{
  "hospitalinfo": {
    "MSSQL_USERNAME": "root",
    "MSSQL_PASSWORD": "mypassword123",
    "MSSQL_HOST": "localhost", 
    "MSSQL_PORT": "3306",
    "MSSQL_DB_NAME": "hospital_db",
    "DB_NAME": "mysql"
  }
}
```

## 运行程序

1. 确保MySQL服务正在运行
2. 确保数据库已创建
3. 更新配置文件中的连接信息
4. 运行程序：
   ```bash
   go run main.go
   ```

## 数据库表结构要求

程序需要以下视图/表存在于MySQL数据库中：

1. **view_spt_patient** - 患者信息视图，包含字段：
   - name (患者姓名)
   - id_card (身份证号)
   - mobile (手机号)
   - sex (性别)
   - age (年龄)
   - hospitalization_no (住院号)
   - sickbed_no (床位号)
   - inhospital_time (入院时间)
   - outhospital_time (出院时间)
   - status (状态：1-在院，2-出院)
   - catetegory (类别)
   - inpatient_ward (病区)
   - doctor_id (医生ID)
   - inpatient_info_id (住院信息ID)
   - nurse_id (护士ID)
   - birthday (生日)

2. **view_spt_user** - 用户信息视图，包含字段：
   - user_name (用户名/医生编号)
   - name (姓名)

## 注意事项

- 确保MySQL用户有足够的权限访问指定的数据库
- 如果MySQL运行在远程服务器上，确保防火墙允许连接
- 程序会自动设置连接池：最大连接数10，最大空闲连接数5
- 确保数据库中存在所需的视图/表结构
- MySQL时间字段应该是DATETIME或TIMESTAMP类型
