version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: collectloop_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: collectloop_db
      MYSQL_USER: hisbridge
      MYSQL_PASSWORD: hisbridge123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql-init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - collectloop_network

  # 可选：添加phpMyAdmin用于数据库管理
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: collectloop_phpmyadmin
    restart: always
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: hisbridge
      PMA_PASSWORD: hisbridge123
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - collectloop_network

volumes:
  mysql_data:

networks:
  collectloop_network:
    driver: bridge
