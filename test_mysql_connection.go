package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 测试MySQL连接
	// 请根据你的实际配置修改这些参数
	username := "root"
	password := "your_password"
	host := "localhost"
	port := "3306"
	dbName := "your_database"

	// MySQL DSN格式
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, dbName)

	fmt.Printf("尝试连接MySQL: %s@%s:%s/%s\n", username, host, port, dbName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("打开数据库连接失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	err = db.<PERSON>()
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("✅ MySQL连接成功!")

	// 测试查询（可选）
	rows, err := db.Query("SELECT 1 as test")
	if err != nil {
		log.Printf("查询测试失败: %v", err)
	} else {
		defer rows.Close()
		if rows.Next() {
			var test int
			rows.Scan(&test)
			fmt.Printf("✅ 查询测试成功，结果: %d\n", test)
		}
	}

	// 检查是否存在所需的表/视图
	tables := []string{"view_spt_patient", "view_spt_user"}
	for _, table := range tables {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '%s' AND table_name = '%s'", dbName, table)).Scan(&count)
		if err != nil {
			log.Printf("检查表 %s 失败: %v", table, err)
		} else if count > 0 {
			fmt.Printf("✅ 表/视图 %s 存在\n", table)
		} else {
			fmt.Printf("❌ 表/视图 %s 不存在\n", table)
		}
	}
}
