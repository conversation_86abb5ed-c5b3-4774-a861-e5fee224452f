package models

import (
	"database/sql"
	"fmt"
	_ "github.com/godror/godror"
	"github.com/thda/tds"
	_ "github.com/thda/tds"
	std "gitlab.itingluo.com/backend/ivankastd"
	"gitlab.itingluo.com/backend/ivankastd/toolkit/strutil"
	"golang.org/x/text/encoding/charmap"
	"log"
)

func CreateSybaseDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = strutil.FromInt64(int64(config.Port))
		dbName   = config.DBName
	)

	tds.RegisterEncoding("cp850", charmap.CodePage850)
	dataSourceName := fmt.Sprintf("tds://%s:%s@%s:%s/%s?language=us_english&charset=cp850", username, password, host, port, dbName)

	db, err := sql.Open("tds", dataSourceName)
	if err != nil {
		log.Fatal(err)
	}
	err = db.Ping()
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println("sybase start")

	fmt.Println(db, err)
	db.SetMaxOpenConns(3)
	db.SetMaxIdleConns(3)
	fmt.Println(33333)
	err = db.Ping()
	fmt.Println(4444)
	if err != nil {
		fmt.Println("connectionString err2", err)
		panic(fmt.Sprintf("Err2 failed to connect oracle %s:%d/%s: %s", host, port, dbName, err.Error()))
	}
	fmt.Println(55555)
	return db
}

func CreateOracleDB(config std.ConfigMssql) *sql.DB {
	var (
		username = config.Username
		password = config.Password
		host     = config.Host
		port     = strutil.FromInt64(int64(config.Port))
		dbName   = config.DBName
	)

	//"**************:1521/orcl"

	dataSourceName := fmt.Sprintf("user=%s password=%s connectString=%s",
		username, password, host)
	fmt.Println(dataSourceName)
	db, err := sql.Open("godror", dataSourceName)
	fmt.Println(db, err)
	if err != nil {
		fmt.Println("connectionString err", err)
		panic(fmt.Sprintf("Err failed to connect oracle %s: %s", dataSourceName, err.Error()))
	}
	db.SetMaxOpenConns(3)
	db.SetMaxIdleConns(3)
	fmt.Println(33333)
	err = db.Ping()
	fmt.Println(4444)
	if err != nil {
		fmt.Println("connectionString err2", err)
		panic(fmt.Sprintf("Err2 failed to connect oracle %s:%d/%s: %s", host, port, dbName, err.Error()))
	}
	fmt.Println(55555)
	return db
}
